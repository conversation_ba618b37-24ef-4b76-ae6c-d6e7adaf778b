const net = require('net');

// 测试端口是否可用的函数
function testPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, (err) => {
      if (err) {
        resolve({ port, available: false, error: err.message });
      } else {
        server.close(() => {
          resolve({ port, available: true, error: null });
        });
      }
    });
    
    server.on('error', (err) => {
      resolve({ port, available: false, error: err.message });
    });
  });
}

// 测试多个端口
async function checkPorts() {
  const portsToTest = [3000, 3001, 3002, 3003, 8080, 8081, 9000];
  
  console.log('=== 端口可用性检查 ===');
  
  for (const port of portsToTest) {
    const result = await testPort(port);
    const status = result.available ? '✅ 可用' : '❌ 不可用';
    console.log(`端口 ${port}: ${status}${result.error ? ` (${result.error})` : ''}`);
  }
}

checkPorts().catch(console.error);
