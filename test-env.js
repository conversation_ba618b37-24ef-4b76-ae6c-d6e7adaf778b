// 测试环境变量加载
console.log('=== 环境变量测试 ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT from process.env:', process.env.PORT);
console.log('所有环境变量中包含PORT的:', Object.keys(process.env).filter(key => key.includes('PORT')));

// 尝试加载.env.local文件
const fs = require('fs');
const path = require('path');

try {
  const envLocalPath = path.join(process.cwd(), '.env.local');
  console.log('查找.env.local文件路径:', envLocalPath);
  
  if (fs.existsSync(envLocalPath)) {
    const envContent = fs.readFileSync(envLocalPath, 'utf8');
    console.log('.env.local文件内容:');
    console.log(envContent);
    console.log('文件大小:', fs.statSync(envLocalPath).size, 'bytes');
  } else {
    console.log('.env.local文件不存在');
  }
} catch (error) {
  console.error('读取.env.local文件时出错:', error.message);
}

// 检查Next.js是否加载了环境变量
console.log('=== Next.js环境变量检查 ===');
try {
  const { loadEnvConfig } = require('@next/env');
  const projectDir = process.cwd();
  loadEnvConfig(projectDir);
  console.log('Next.js加载环境变量后的PORT:', process.env.PORT);
} catch (error) {
  console.error('Next.js环境变量加载失败:', error.message);
}
